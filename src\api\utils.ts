export const baseUrlApi = (url: string) => {
  // 确保 url 以 'api/' 开头
  const normalizedUrl = url.startsWith("api/") ? url : `api/${url}`;
  return `/${normalizedUrl}`;
};

export const simpleUploadUrl = () => {
  return process.env.NODE_ENV === "development"
    ? "api/api/v1/fs/upload_chunk"
    : "api/v1/fs/upload_chunk";
};

export const downLoadAudioFileUrl =
  process.env.NODE_ENV === "development"
    ? "api/api/v1/voice2text/result/"
    : "api/v1/voice2text/result/";

export const downLoadPdfFileUrl =
  process.env.NODE_ENV === "development"
    ? "api/api/v1/pdf2word/result/"
    : "api/v1/pdf2word/result/";

export const downLoadOfdPdfFileUrl =
  process.env.NODE_ENV === "development"
    ? "api/api/v1/ofd2pdf/result/"
    : "api/v1/ofd2pdf/result/";

export const downLoadOfdWordFileUrl =
  process.env.NODE_ENV === "development"
    ? "api/api/v1/ofd2word/result/"
    : "api/v1/ofd2word/result/";
