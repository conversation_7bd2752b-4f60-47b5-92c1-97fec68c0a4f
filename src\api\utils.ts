export const baseUrlApi = (url: string) => {
  // 如果 url 已经以 'api/' 开头，就不再添加 '/api/' 前缀
  if (url.startsWith("api/")) {
    return `/${url}`;
  }
  return `/api/${url}`;
};

export const simpleUploadUrl = () => {
  return process.env.NODE_ENV === "development"
    ? "api/api/v1/fs/upload_chunk"
    : "api/v1/fs/upload_chunk";
};

export const downLoadAudioFileUrl =
  process.env.NODE_ENV === "development"
    ? "api/api/v1/voice2text/result/"
    : "api/v1/voice2text/result/";

export const downLoadPdfFileUrl =
  process.env.NODE_ENV === "development"
    ? "api/api/v1/pdf2word/result/"
    : "api/v1/pdf2word/result/";

export const downLoadOfdPdfFileUrl =
  process.env.NODE_ENV === "development"
    ? "api/api/v1/ofd2pdf/result/"
    : "api/v1/ofd2pdf/result/";

export const downLoadOfdWordFileUrl =
  process.env.NODE_ENV === "development"
    ? "api/api/v1/ofd2word/result/"
    : "api/v1/ofd2word/result/";
